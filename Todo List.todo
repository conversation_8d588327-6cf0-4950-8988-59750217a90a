先給你關於闖關系統上 這兩天操作上須要優化的地方：

🔧【闖關系統｜功能需求調整與新增】
✅【修改功能】獎勵設定中：返點獎勵邏輯需可依「學員級別」差異化設定
目前邏輯問題：返點金額為固定值，無法因應不同級別設定差異。
請改為：
後台可針對不同級別（如：初階／進階／高階）分別設定對應的返點金額（點數）。

例如：
  "一般學員": 200,
  "青銅學員": 500,
  "鑽石學員": 800

學員完成對應任務後，自動依其當前級別發放對應返點。

🆕【新增功能】獎勵設定中新增一種獎勵類型：「領取周邊」
新增獎勵類型選項：領取周邊
若選擇此類型，後台需出現以下欄位供設定：

「周邊名稱」文字欄（可自由輸入）
「收件表單網址」欄位（填入 Google 表單連結）

前台流程行為：
學員完成任務並點選領取獎勵後，若為「領取周邊」，則：
顯示已成功領取訊息
自動跳轉至設定好的 Google 表單連結，供填寫收件資料

🔄【分類邏輯調整】闖關條件應支援依「課費金額」條件判定
目前邏輯問題：任務完成條件僅能依照任務次數、完成件數等方式設定。

請新增支援條件類型：依「當月課費金額」判定是否達標。
設定方式範例：
任務條件設定中新增「金額條件」選項：

當月課費金額 ≧ 30,000 點
學員於當月達成此金額，即可解鎖該階段任務與對應獎勵。

說明：金額來源需串接後台課費統計資料，並以「單月累積課費」為依據。