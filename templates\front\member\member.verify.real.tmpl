<!DOCTYPE html>
<html>
    <head>
        {{template "mem.reg.assets" .}}
    </head>
    <body>
        {{template "top" .}}

        {{template "member.verify.real" .}}

        {{template "bottom" .}}
    </body>
    <script>
        createApp({
            delimiters: ['${', '}'],
            mixins: [Member, CommonMixin, RegVerifyMix],
            data() {
                return {
                    index: 'real',
                    real: {},
                    startVerify: false,
                    isUploading: false,
                    timer: null,
                    countdownTime: '',
                    video: null,    // 影片檔案
                    interval: 1000,
                }
            },
            methods: {
                getData() {
                    axiosRequest()
                        .get('/api/members/verify/real')
                        .then(res => {
                            this.real = res.data.data

                            const status = this.real.status
                            if (status === VS.ING || status === VS.FAIL) {
                                if (this.real.quest_content != "" && 
                                    !this.isZeroDate(this.real.expired_at) && !this.isDateTimeExpired(this.real.expired_at)) {
                                    this.startTimer()
                                }
                            }
                        })
                        .catch(err => {
                            console.log(err)
                            console.log(err.response.data)
                            msgError(err.response.data.msg)
                        })
                },
                async startRealVerify() {
                    msgLoading()

                    await axiosRequest()
                        .post('/api/members/verify/real')
                        .then(res => {
                            const data = res.data.data

                            if (!data.quest_content) {
                                msgError('驗證內容尚未設定，請通知官方進行設定')
                                return
                            }

                            this.real.quest_content = data.quest_content
                            this.real.expired_at = data.expired_at

                            this.startTimer()

                            Swal.close()
                        })
                        .catch(err => {
                            console.log(err)
                            console.log(err.response.data)
                            msgError(err.response.data.msg)
                        })
                },
                startTimer() {
                    this.startVerify = true
                    this.timer = setInterval(this.updateTimer, this.interval)
                },
                updateTimer() {
                    const expiredAt = moment(this.real.expired_at)
                    const diff = expiredAt.diff(moment())
                    const duration = moment.duration(diff)

                    duration.subtract(this.interval)

                    if (duration.asMilliseconds() <= 0) {
                        this.timeUp()
                        return
                    }

                    this.countdownTime = moment.utc(duration.asMilliseconds()).format('mm:ss')
                },
                timeUp() {
                    clearInterval(this.timer)
                    this.countdownTime = '00:00'
                    this.startVerify = false

                    // 時間到期後自動重新生成驗證內容
                    msgError('驗證時間已到，系統將重新生成驗證內容', '', false)

                    // 延遲一秒後重新生成驗證內容
                    setTimeout(() => {
                        this.regenerateQuest()
                    }, 1000)
                },
                async regenerateQuest() {
                    msgLoading('正在重新生成驗證內容...')

                    try {
                        await axiosRequest()
                            .post('/api/members/verify/real')
                            .then(res => {
                                const data = res.data.data

                                if (!data.quest_content) {
                                    msgError('驗證內容尚未設定，請通知官方進行設定')
                                    return
                                }

                                this.real.quest_content = data.quest_content
                                this.real.expired_at = data.expired_at

                                msgSuccess('已重新生成驗證內容，請重新開始驗證')

                                Swal.close()
                            })
                            .catch(err => {
                                console.log(err)
                                console.log(err.response.data)
                                msgError(err.response.data.msg)
                            })
                    } catch (error) {
                        console.error('重新生成驗證內容失敗:', error)
                        msgError('重新生成驗證內容失敗，請重新整理頁面')
                    }
                },
                async videoLoad(e) {
                    this.video = e.target.files[0]
                },
                async submitRealVideo() {
                    if (!this.video) {
                        msgError('請選擇影片檔案')
                        return
                    }

                    const file = this.video
                    const fileName = '真人驗證影片'
                    const chunkSize = 1024 * 1024 * 1; // 1MB per chunk
                    const totalChunks = Math.ceil(file.size / chunkSize);

                    // 顯示上傳進度視窗
                    msgUploadProgress(fileName, file.size);

                    try {
                        clearInterval(this.timer);

                        for (let chunkIndex = 0; chunkIndex < totalChunks; chunkIndex++) {
                            const start = chunkIndex * chunkSize;
                            const end = Math.min(file.size, start + chunkSize);
                            const chunk = file.slice(start, end);

                            const formData = new FormData();
                            formData.append('file', chunk, file.name);
                            formData.append('chunkIndex', chunkIndex);
                            formData.append('totalChunks', totalChunks);

                            // 更新進度 (在發送前)
                            updateUploadProgress(chunkIndex + 1, totalChunks, fileName);
                            
                            await axiosRequest('form-data')
                                .patch('/api/members/verify/real', formData)
                                
                            if (chunkIndex === totalChunks - 1) {
                                setTimeout(() => {
                                    msgSuccess("上傳成功！")
                                }, 500);
                            }
                        }
                    } catch (error) {
                        console.error('Upload failed:', error);
                        msgError('上傳失敗')
                    } finally {
                        this.getVerify()
                        this.getData()
                        $('#real_video').val('')
                        this.video = null
                    }
                },
                formatQuestContent(content) {
                    if (!content) return ''
                    return content.replace(/\n/g, '<br>')
                }
            },
        }).mount('#app')
    </script>
</html>

{{define "member.verify.real"}}
    <div id="app" class="memb_top">
        <div class="container">
            {{template "dow_now" .}}

            {{template "txt_title" .}}

            {{template "member.verify.step" .}}

            {{template "member.verify.status" .}}
            
            {{template "member.verify.notice" .}}
            
            <div class="content_box">
                <template v-if="isStepWaiting(step) || isStepCompleted(step)">
                    <!-- 引導提示 -->
                    <div class="content_box mb-3" data-aos="fade-up">
                        <div class="cont cont_w100">
                            <h5 class="text-center mb-3">✅ 真人驗證已完成</h5>
                            <p class="text-center text-muted">您可以繼續進行下一個步驟</p>
                        </div>
                    </div>

                    <div class="cont_w100">
                        <h5>
                            <span>驗證內容：</span>
                            <span>
                                <div class="fw-bold text-gold" v-html="formatQuestContent(real.quest_content)"></div>
                            </span>
                        </h5>
                    </div>

                    <div class="embed-responsive embed-responsive-4by3 text-center my-4">
                        <video class="embed-responsive-item" controls style="height:300px; max-width:100%;">
                            <source :src="`/${real.video_path}`" type="video/mp4">
                        </video>
                    </div>

                    <div class="text-center">
                        <button type="button" onclick="window.location.href='/member/verify/report'" class="btn btn-gold">
                            前往下一步：筆記回傳
                        </button>
                    </div>
                </template>

                <template v-else>
                    <div class="memb_btn text-center" v-if="!startVerify">
                        <div class="return_q mb-3">
                            <ul>
                                <li>
                                    <button type="button" @click="startRealVerify">
                                        <template v-if="isStepOverdue(step) || isStepFail(step)">重新驗證</template>
                                        <template v-else>開始驗證</template>
                                    </button>
                                </li>
                            </ul>
                        </div>
                        <p>※請準備好錄製再點擊按鈕！</p>
                    </div>
    
                    <div v-if="startVerify">
                        <span class="mb-3">請依以下指示錄製影片，提及內文必須一模一樣：</span>
                        <p class="mb-3">
                            <font class="fw-bold" color="#b99764" v-html="formatQuestContent(real.quest_content)"></font>
                        </p>
                        <div>
                            <input type="file" id="real_video" @change="videoLoad" accept="video/*" capture="environment" class="form-control w-auto mx-0 mb-3">
    
                            <button type="button" @click="submitRealVideo" class="btn btn-success mb-3">上傳</button>
                        </div>
                        <span>
                            請於
                            <strong class="text-gold" v-text="countdownTime"></strong>
                            內上傳影片，若超過指定時間，視為驗證失敗<br>
                            ※ 影片上傳完成後，請耐心等待審核結果，謝謝。
                        </span>
                    </div>
                </template>
            </div>
        </div>
    </div>
{{end}}