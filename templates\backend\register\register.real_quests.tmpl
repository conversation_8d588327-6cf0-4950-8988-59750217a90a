<!DOCTYPE html>
<html>
    <head>
        {{template "head" .}}
        {{template "sweet_alert" .}}
        {{template "validate" .}}
    </head>
    <body>
        {{template "top" .}}

        {{template "admin/register.real_quests" .}}

        {{template "bottom" .}}
    </body>
</html>

<script>
    createApp({
        delimiters: ['${', '}'],
        mixins: [CommonMixin, TablePageMixin],
        data() {
            return {
                items: [],
                search: {
                    content: '',
                    status: ''
                }
            }
        },
        mounted() {
            this.getData()
        },
        methods: {
            getData() {
                msgLoading()

                const params = {
                    search: this.search,
                    ... this.pageOption
                }

                axiosRequest()
                    .get('/api/admin/members/verify/real/quests', { params })
                    .then(res => {
                        this.items = res.data.data
                        this.bindJSON(this.pageOption, res.data.page)
                        Swal.close()
                    })
                    .catch(err => {
                        console.log(err)
                        msgError(err.response.data.msg)
                    })
            },
            deleteItem(id) {
                msgConfirm('確定要刪除嗎？',() => {
                    axiosRequest()
                        .delete(`/api/admin/members/verify/real/quests/${id}`)
                        .then(res => {
                            msgTopSuccess(res.data.msg)
                            this.getData()
                        })
                        .catch(err => {
                            console.log(err)
                            msgError(err.response.data.msg)
                        })
                })
            },
            getStatusText(status) {
                return status === 'Y' ? '啟用' : '停用'
            },
            getStatusClass(status) {
                return status === 'Y' ? 'text-success' : 'text-danger'
            }
        }
    }).mount('#app')
</script>

{{define "admin/register.real_quests"}}
    <div id="app" class="content_box">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h4>真人驗證範本管理</h4>
            <a href="/admin/member/real-quests/create" class="btn btn-success">
                <i class="fas fa-plus"></i> 新增範本
            </a>
        </div>

        <!-- 搜尋區域 -->
        <div class="card mb-3">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <label class="form-label">範本內容</label>
                        <input type="text" v-model="search.content" @keyup.enter="getData" class="form-control" placeholder="搜尋範本內容">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">狀態</label>
                        <select v-model="search.status" class="form-select">
                            <option value="">全部</option>
                            <option value="Y">啟用</option>
                            <option value="N">停用</option>
                        </select>
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button @click="getData" class="btn btn-primary me-2">搜尋</button>
                        <button @click="search = {content: '', status: ''}; getData()" class="btn btn-secondary">清除</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 範本列表 -->
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <!-- <th width="60">ID</th> -->
                                <th>範本內容</th>
                                <th width="100">時間限制</th>
                                <th width="80">狀態</th>
                                <th width="150">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="item in items" :key="item.id">
                                <!-- <td>${ item.id }</td> -->
                                <td>
                                    <div class="text-truncate" style="max-width: 300px;" :title="item.content">
                                        ${ item.content }
                                    </div>
                                </td>
                                <td>${ item.limit_time } 分鐘</td>
                                <td>
                                    <span :class="getStatusClass(item.status)" class="fw-bold">
                                        ${ getStatusText(item.status) }
                                    </span>
                                </td>
                                <td>
                                    <a :href="`/admin/member/real-quests/${item.id}/edit`" class="btn btn-sm btn-outline-primary me-1">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button @click="deleteItem(item.id)" class="btn btn-sm btn-outline-danger">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr v-if="items.length === 0">
                                <td colspan="6" class="text-center text-muted py-4">
                                    暫無資料
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                {{template "page_nav" .}}
            </div>
        </div>
    </div>
{{end}}
