<!DOCTYPE html>
<html>
    <head>
        {{template "head" .}}
        {{template "sweet_alert" .}}
        {{template "validate" .}}
    </head>
    <body>
        {{template "top" .}}

        {{template "admin/register.real_quest_reg" .}}

        {{template "bottom" .}}
    </body>
</html>

<script>
    createApp({
        delimiters: ['${', '}'],
        mixins: [CommonMixin],
        data() {
            return {
                mode: '{{ .mode }}',
                id: '{{ .id }}',
                data: {
                    id: 0,
                    content: '',
                    limit_time: 5,
                    status: 'Y'
                }
            }
        },
        mounted() {
            if (this.mode === 'edit' && this.id > 0) {
                this.getData()
            }

            $('#main_form').validate({
                rules: {
                    content: {
                        required: true,
                        minlength: 10
                    },
                    limit_time: {
                        required: true,
                        min: 1,
                        max: 15
                    }
                },
                messages: {
                    content: {
                        required: "請輸入範本內容",
                        minlength: "範本內容至少需要10個字元"
                    },
                    limit_time: {
                        required: "請選擇時間限制",
                        min: "時間限制最少1分鐘",
                        max: "時間限制最多15分鐘"
                    }
                }
            })
        },
        methods: {
            getData() {
                msgLoading()

                axiosRequest()
                    .get(`/api/admin/members/verify/real/quests/${this.id}`)
                    .then(res => {
                        this.data = res.data.data
                        Swal.close()
                    })
                    .catch(err => {
                        console.log(err)
                        msgError(err.response.data.msg)
                    })
            },
            submitData() {
                if (!$('#main_form').valid()) {
                    return
                }

                msgLoading()

                const method = this.mode === 'edit' ? 'patch' : 'post'
                const url = this.mode === 'edit' 
                    ? `/api/admin/members/verify/real/quests/${this.id}`
                    : '/api/admin/members/verify/real/quests'

                axiosRequest()
                    [method](url, this.data)
                    .then(res => {
                        const msg = this.mode === 'edit' ? '更新成功' : '新增成功'
                        msgSuccess(msg, '/admin/member/real-quests')
                    })
                    .catch(err => {
                        console.log(err)
                        msgError(err.response.data.msg)
                    })
            },
            insertVariable(variable) {
                const textarea = document.querySelector('textarea[name="content"]')
                const start = textarea.selectionStart
                const end = textarea.selectionEnd
                const text = textarea.value
                const before = text.substring(0, start)
                const after = text.substring(end, text.length)
                
                this.data.content = before + variable + after
                
                // 設置游標位置
                this.$nextTick(() => {
                    textarea.focus()
                    textarea.setSelectionRange(start + variable.length, start + variable.length)
                })
            }
        },
        computed: {
            pageTitle() {
                return this.mode === 'edit' ? '編輯範本' : '新增範本'
            }
        }
    }).mount('#app')
</script>

{{define "admin/register.real_quest_reg"}}
    <div id="app" class="content_box">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h4>${ pageTitle }</h4>
            <a href="/admin/member/real-quests" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> 返回列表
            </a>
        </div>

        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-body">
                        <form id="main_form" @submit.prevent="submitData">
                            <div class="mb-3">
                                <label class="form-label">範本內容 <span class="text-danger">*</span></label>
                                <textarea name="content" v-model="data.content" rows="6" class="form-control" 
                                    placeholder="請輸入驗證範本內容，可使用右側的變數"></textarea>
                                <div class="form-text">
                                    請輸入會員需要錄製的內容，可以使用右側的變數來動態替換會員資料
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">時間限制 <span class="text-danger">*</span></label>
                                        <select name="limit_time" v-model="data.limit_time" class="form-select">
                                            <option v-for="i in 15" :value="i" :key="i">${ i } 分鐘</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">狀態</label>
                                        <select v-model="data.status" class="form-select">
                                            <option value="Y">啟用</option>
                                            <option value="N">停用</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save"></i> 儲存
                                </button>
                                <a href="/admin/member/real-quests" class="btn btn-secondary">取消</a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <!-- 變數插入工具 -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">📝 範本變數</h6>
                    </div>
                    <div class="card-body">
                        <p class="small text-muted mb-3">點擊下方按鈕將變數插入到範本內容中</p>
                        
                        <div class="d-grid gap-2">
                            <button type="button" @click="insertVariable('{name}')" class="btn btn-outline-primary btn-sm">
                                <code>{name}</code> - 會員姓名
                            </button>
                            <button type="button" @click="insertVariable('{english_name}')" class="btn btn-outline-primary btn-sm">
                                <code>{english_name}</code> - 會員英文名字
                            </button>
                            <button type="button" @click="insertVariable('{nickname}')" class="btn btn-outline-primary btn-sm">
                                <code>{nickname}</code> - 會員暱稱
                            </button>
                            <button type="button" @click="insertVariable('{city}')" class="btn btn-outline-primary btn-sm">
                                <code>{city}</code> - 城市
                            </button>
                            <button type="button" @click="insertVariable('{area}')" class="btn btn-outline-primary btn-sm">
                                <code>{area}</code> - 區域
                            </button>
                            <button type="button" @click="insertVariable('{first_contact_channel}')" class="btn btn-outline-primary btn-sm">
                                <code>{first_contact_channel}</code> - 第一次接觸 G.R.A.C.E 是透過什麼樣的管道
                            </button>
                            <button type="button" @click="insertVariable('{referrer}')" class="btn btn-outline-primary btn-sm">
                                <code>{referrer}</code> - 介紹人
                            </button>
                            <button type="button" @click="insertVariable('{future_study_direction}')" class="btn btn-outline-primary btn-sm">
                                <code>{future_study_direction}</code> - 未來希望進修之方向說明
                            </button>
                            <button type="button" @click="insertVariable('{desired_level}')" class="btn btn-outline-primary btn-sm">
                                <code>{desired_level}</code> - 希望申請學員級別
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 範例 -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">💡 範例</h6>
                    </div>
                    <div class="card-body">
                        <div class="small">
                            <p class="mb-2"><strong>範例1：</strong></p>
                            <p class="text-muted mb-3">請說出您的中文姓名：{name}，英文名字：{english_name}，暱稱：{nickname}</p>

                            <p class="mb-2"><strong>範例2：</strong></p>
                            <p class="text-muted mb-0">我是{name}，住在{city}{area}，透過{first_contact_channel}認識G.R.A.C.E，希望申請{desired_level}級別</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{{end}}
