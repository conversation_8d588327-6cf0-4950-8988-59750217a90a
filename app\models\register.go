package models

import (
	"fmt"
	"strings"
	"time"

	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"
)

const (
	OfficialImgPath = "uploads/mem_reg/official/"
	RealVideoPath   = "uploads/mem_reg/real"
	MemReportPath   = "/uploads/mem_reg/report/"
	QuizFilePath    = "uploads/quiz/"
	MemQuizImgPath  = "/uploads/mem_reg/quiz"
)

// member register verify status
type VerifyStatus string

const (
	VerifyUnSet   VerifyStatus = ""  // 未設定
	VerifyIng     VerifyStatus = "I" // 考核中
	VerifyWait    VerifyStatus = "W" // 待審核
	VerifyYes     VerifyStatus = "Y" // 通過
	VerifyOverdue VerifyStatus = "O" // 逾期
	VerifyNo      VerifyStatus = "N" // 退回/未通過
	VerifyCancel  VerifyStatus = "C" // 取消
	VerifyDelete  VerifyStatus = "D" // 刪除
)

const (
	VerifyStepInfo     = 1 // 基本資料
	VerifyStepOfficial = 2 // 官方帳號
	VerifyStepReal     = 3 // 真人驗證
	VerifyStepReport   = 4 // 筆記回傳
	VerifyStepQuiz     = 5 // 入學筆試
)

// member verify url by step
var MemberVerifyUrl = map[int]string{
	VerifyStepInfo:     "/member/verify/info",
	VerifyStepOfficial: "/member/verify/official",
	VerifyStepReal:     "/member/verify/real",
	VerifyStepQuiz:     "/member/verify/quiz",
	VerifyStepReport:   "/member/verify/report",
}

type MemReg struct {
	ID         uint           `form:"id" json:"id"`
	MemberID   uint           `gorm:"<-:create" form:"member_id" json:"member_id"`
	VerifyStep int            `form:"verify_step" json:"verify_step"`
	Info       MemRegInfo     `gorm:"foreignKey:MemRegID;references:ID" json:"info"`
	Official   MemRegOfficial `gorm:"foreignKey:MemRegID;references:ID" json:"official"`
	Real       MemRegReal     `gorm:"foreignKey:MemRegID;references:ID" json:"real"`
	Quiz       MemRegQuiz     `gorm:"foreignKey:MemRegID;references:ID" json:"quiz"`
	Report     MemRegReport   `gorm:"foreignKey:MemRegID;references:ID" json:"report"`
	CreatedAt  time.Time      `gorm:"<-:create" json:"created_at"`
	UpdatedAt  time.Time      `json:"updated_at"`
	DeletedAt  gorm.DeletedAt `gorm:"<-:false" json:"deleted_at"`
}

func (reg *MemReg) CheckVerifyStep(conn *gorm.DB) error {
	newStep := reg.getNewVerifyStep()

	if newStep != reg.VerifyStep {
		if err := conn.Model(&MemReg{}).Where("member_id = ?", reg.MemberID).
			Update("verify_step", newStep).Error; err != nil {
			return err
		}
	}

	return nil
}

func (reg *MemReg) getNewVerifyStep() int {
	// 正常流程：依序檢查每個階段
	if reg.isStatusUnFinished(reg.Info.Status) {
		return VerifyStepInfo
	}

	if reg.isStatusUnFinished(reg.Official.Status) {
		return VerifyStepOfficial
	}

	if reg.isStatusUnFinished(reg.Real.Status) {
		return VerifyStepReal
	}

	// Info、Official、Real 都完成後，決定下一步
	// 特殊情況：Report 和 Quiz 都尚未設定（UnSet），則待在 Real
	if reg.Report.Status == VerifyUnSet && reg.Quiz.Status == VerifyUnSet {
		return VerifyStepReal
	}

	// 檢查 Quiz 是否為 Ing 狀態
	if reg.Quiz.Status == VerifyIng {
		return VerifyStepQuiz
	}

	// 如果 Quiz 不是 Ing，則檢查 Report
	if reg.Report.Status != VerifyUnSet && reg.isStatusUnFinished(reg.Report.Status) {
		return VerifyStepReport
	}

	// 如果 Report 也完成了，再檢查 Quiz
	if reg.Quiz.Status != VerifyUnSet && reg.isStatusUnFinished(reg.Quiz.Status) {
		return VerifyStepQuiz
	}

	// 所有階段都完成，維持當前步驟
	return reg.VerifyStep
}

func (reg *MemReg) isStatusUnFinished(status VerifyStatus) bool {
	return status != VerifyYes && status != VerifyWait
}

type MemRegBase struct {
	MemRegID     uint           `gorm:"<-:create" form:"mem_reg_id" json:"mem_reg_id"`
	MemberID     uint           `gorm:"<-:create" form:"member_id" json:"member_id"`
	Status       VerifyStatus   `form:"status" json:"status"` // 狀態，I:進行中(預設), W:待審核, Y:通過, N:未通過
	Note         string         `form:"note" json:"note"`     // 備註
	SubmittedAt  null.Time      `form:"submitted_at" json:"submitted_at"`
	VerifiedAt   null.Time      `gorm:"<-:update" form:"verified_at" json:"verified_at"`
	VerifiedByID null.Int       `gorm:"<-:update" form:"verified_by_id" json:"verified_by_id"`
	VerifiedBy   string         `gorm:"<-:false" json:"verified_by"`
	CreatedAt    time.Time      `gorm:"<-:create" form:"created_at" json:"created_at"`
	UpdatedAt    time.Time      `form:"updated_at" json:"updated_at"`
	DeletedAt    gorm.DeletedAt `gorm:"<-:false" form:"deleted_at" json:"deleted_at"`
}

func LoadVerifiedBy(db *gorm.DB) *gorm.DB {
	return db.Select("*", "(SELECT admin_name FROM admins WHERE id = verified_by_id) AS verified_by")
}

type MemRegInfo struct {
	ID         uint   `form:"id" json:"id"`
	Know       string `form:"know" json:"know"`               // 從哪裡知道我們
	Introducer string `form:"introducer" json:"introducer"`   // 介紹人
	Job        string `form:"job" json:"job"`                 // 職業
	Future     string `form:"future" json:"future"`           // 未來學習方向
	ApplyLevel uint   `form:"apply_level" json:"apply_level"` // 想申請的會員等級
	MemRegBase `gorm:"embedded"`
}

type MemRegOfficial struct {
	ID         uint   `form:"id" json:"id"`
	ImgPath    string `form:"img_path" json:"img_path"`
	MemRegBase `gorm:"embedded"`
}

type MemRegReal struct {
	ID           uint      `form:"id" json:"id"`
	RealQuestID  null.Int  `form:"real_quest_id" json:"real_quest_id"`
	QuestContent string    `form:"quest_content" json:"quest_content"`
	LimitTime    int       `form:"limit_time" json:"limit_time"`
	VideoPath    string    `form:"video_path" json:"video_path"`
	ExpiredAt    null.Time `form:"expired_at" json:"expired_at"`
	MemRegBase   `gorm:"embedded"`
}

func (real *MemRegReal) GetQuest(conn *gorm.DB, memberID uint, start bool) error {
	if err := conn.Select("id", "quest_content", "limit_time", "video_path", "expired_at", "status", "real_quest_id").
		Where("member_id = ?", memberID).Find(&real).Error; err != nil {
		return err
	}

	// 檢查是否需要生成新的驗證內容
	needNewQuest := start || real.QuestContent == "" || real.IsQuestExpired()

	if needNewQuest {
		if err := real.GenerateNewQuest(conn, memberID); err != nil {
			return err
		}

		if real.LimitTime == 0 {
			real.LimitTime = 5
		}

		t := time.Now().Add(time.Duration(real.LimitTime) * time.Minute)
		real.ExpiredAt = null.TimeFrom(t)
		real.Status = VerifyIng
		if err := conn.Select("expired_at", "status").Updates(&real).Error; err != nil {
			return err
		}
	} else if err := real.CheckQuestExpired(conn); err != nil {
		return err
	}

	return nil
}

func (real *MemRegReal) CheckQuestExpired(conn *gorm.DB) error {
	if real.ExpiredAt.Time.IsZero() || real.Status != VerifyIng {
		return nil
	}

	if real.ExpiredAt.Time.Before(time.Now()) {
		if err := conn.Model(&real).Update("status", VerifyOverdue).Error; err != nil {
			return err
		}
		return fmt.Errorf("quest expired")
	}

	return nil
}

func (real *MemRegReal) IsQuestExpired() bool {
	return real.Status == VerifyIng && real.ExpiredAt.Time.Before(time.Now())
}

// GenerateNewQuest 生成新的驗證內容
func (real *MemRegReal) GenerateNewQuest(conn *gorm.DB, memberID uint) error {
	// 獲取會員資料
	member := Member{}
	if err := conn.Select("name", "en_name", "nick_name", "address", "city", "area", "zipcode", "phone", "line_id").
		Where("id = ?", memberID).First(&member).Error; err != nil {
		return err
	}

	// 隨機選擇一個啟用的範本
	quest := RegRealQuest{}
	if err := conn.Where("status = ?", "Y").Order("RAND()").First(&quest).Error; err != nil {
		return fmt.Errorf("沒有可用的驗證範本")
	}

	// 進行範本替換
	content := real.ReplaceTemplate(quest.Content, member, memberID, conn)

	// 更新驗證內容
	real.RealQuestID = null.IntFrom(int64(quest.ID))
	real.QuestContent = content
	real.LimitTime = quest.LimitTime
	real.ExpiredAt = null.Time{} // 重置過期時間
	real.Status = VerifyUnSet

	if err := conn.Select("real_quest_id", "quest_content", "limit_time", "expired_at", "status").
		Where("member_id = ?", memberID).Updates(&real).Error; err != nil {
		return err
	}

	return nil
}

// ReplaceTemplate 替換範本中的變數
func (real *MemRegReal) ReplaceTemplate(template string, member Member, memberID uint, conn *gorm.DB) string {
	content := template

	// 替換會員姓名
	content = strings.ReplaceAll(content, "{name}", member.Name)

	// 替換會員英文名字
	englishName := ""
	if member.EnName != nil {
		englishName = *member.EnName
	}
	content = strings.ReplaceAll(content, "{english_name}", englishName)

	// 替換會員暱稱
	content = strings.ReplaceAll(content, "{nickname}", member.NickName)

	// 替換城市
	content = strings.ReplaceAll(content, "{city}", member.City)

	// 替換區域
	content = strings.ReplaceAll(content, "{area}", member.Area)

	// 獲取會員註冊資訊
	regInfo := MemRegInfo{}
	if err := conn.Select("know", "introducer", "future", "apply_level").
		Where("member_id = ?", memberID).First(&regInfo).Error; err == nil {

		// 替換第一次接觸 G.R.A.C.E 的管道
		content = strings.ReplaceAll(content, "{first_contact_channel}", regInfo.Know)

		// 替換介紹人
		content = strings.ReplaceAll(content, "{referrer}", regInfo.Introducer)

		// 替換未來希望進修之方向說明
		content = strings.ReplaceAll(content, "{future_study_direction}", regInfo.Future)

		// 替換希望申請學員級別
		levelName := ""
		switch regInfo.ApplyLevel {
		case 1:
			levelName = "一般"
		case 2:
			levelName = "青銅"
		case 3:
			levelName = "鑽石"
		case 4:
			levelName = "至尊"
		case 5:
			levelName = "皇家至尊"
		default:
			levelName = "未指定"
		}
		content = strings.ReplaceAll(content, "{desired_level}", levelName)
	}

	return content
}

type RegRealQuest struct {
	ID          uint           `form:"id" json:"id"`
	LimitTime   int            `form:"limit_time" json:"limit_time"`
	Content     string         `form:"content" json:"content"`
	Status      string         `form:"status" json:"status"`
	CreatedByID uint           `form:"created_by_id" json:"created_by_id"`
	CreatedBy   string         `gorm:"-" json:"created_by"`
	UpdatedByID uint           `form:"updated_by_id" json:"updated_by_id"`
	UpdatedBy   string         `gorm:"-" json:"updated_by"`
	CreatedAt   time.Time      `gorm:"<-:create" form:"created_at" json:"created_at"`
	UpdatedAt   time.Time      `form:"updated_at" json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"<-:false" form:"deleted_at" json:"deleted_at"`
}

type RealQuest struct {
	ID           uint   `json:"id"`
	QuestContent string `json:"quest_content"`
	LimitTime    int    `json:"limit_time"`
}

type RealQuestItem struct {
	ID          uint           `form:"id" json:"id"`
	Name        string         `form:"name" json:"name"`
	Status      string         `form:"status" json:"status"`
	Sorting     uint           `form:"sorting" json:"sorting"`
	CreatedByID uint           `form:"created_by_id" json:"created_by_id"`
	CreatedBy   string         `gorm:"-" json:"created_by"`
	UpdatedByID uint           `form:"updated_by_id" json:"updated_by_id"`
	UpdatedBy   string         `gorm:"-" json:"updated_by"`
	CreatedAt   time.Time      `gorm:"<-:create" form:"created_at" json:"created_at"`
	UpdatedAt   time.Time      `form:"updated_at" json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"<-:false" form:"deleted_at" json:"deleted_at"`
}

// GetRandomItems 獲取啟用的隨機物品列表
func GetRandomItems(conn *gorm.DB) ([]string, error) {
	var items []RealQuestItem
	if err := conn.Select("name").Where("status = ?", "Y").Order("sorting ASC").Find(&items).Error; err != nil {
		return nil, err
	}

	var names []string
	for _, item := range items {
		names = append(names, item.Name)
	}

	return names, nil
}

type MemRegQuiz struct {
	ID         uint      `form:"id" json:"id"`
	StartAt    null.Time `form:"start_at" json:"start_at"`
	EndAt      null.Time `form:"end_at" json:"end_at"`
	QuizLink   string    `form:"quiz_link" json:"quiz_link"`
	Img        string    `form:"img" json:"img"`
	UploadAt   null.Time `form:"upload_at" json:"upload_at"`
	MemRegBase `gorm:"embedded"`
}

func (quiz *MemRegQuiz) CheckQuizExpired(conn *gorm.DB) error {
	if quiz.EndAt.IsZero() || quiz.Status != VerifyIng {
		return nil
	}

	if time.Now().After(quiz.EndAt.Time.Add(time.Minute)) {
		if err := conn.Model(&quiz).Update("status", VerifyWait).Error; err != nil {
			return err
		}
	}

	return nil
}

// type MemRegQuiz struct {
// 	ID         uint      `form:"id" json:"id"`
// 	MemQuizID  uint      `form:"mem_quiz_id" json:"mem_quiz_id"`
// 	ExpiredAt  null.Time `form:"expired_at" json:"expired_at"`
// 	MemRegBase `gorm:"embedded"`
// }

// 會員取得的考題
type MemQuiz struct {
	ID         uint         `form:"id" json:"id"`
	QuizID     uint         `form:"quiz_id" json:"quiz_id"`
	ExpiredAt  null.Time    `form:"expired_at" json:"expired_at"`
	QuizAns    []MemQuizAns `gorm:"foreignKey:MemQuizID;references:ID" form:"quiz_ans" json:"quiz_ans"`
	MemRegBase `gorm:"embedded"`
}

type MemQuizAns struct {
	ID        uint           `form:"id" json:"id"`
	MemQuizID uint           `form:"mem_quiz_id" json:"mem_quiz_id"`
	QuestID   uint           `form:"quest_id" json:"quest_id"`
	AnsNum    int            `form:"ans_num" json:"ans_num"` // 第幾個答案
	Ans       string         `form:"ans" json:"ans"`
	CreatedAt time.Time      `gorm:"<-:create" form:"created_at" json:"created_at"`
	UpdatedAt time.Time      `form:"updated_at" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"<-:false" form:"deleted_at" json:"deleted_at"`
}

type MemberVerifyHistory struct {
	ID               uint         `gorm:"primaryKey" form:"id" json:"id"`
	MemberID         uint         `form:"member_id" json:"member_id"`
	VerifyStep       int          `form:"verify_step" json:"verify_step"`
	Status           VerifyStatus `form:"status" json:"status"`                                       // 狀態，I:考核中(預設), W:待審核, Y:通過, N:未通過
	RejectReason     string       `form:"reject_reason" json:"reject_reason"`                         // 未通過原因
	UpdatedType      string       `form:"updated_type" json:"updated_type"`                           // 更新類型，A:新增, U:更新, D:刪除
	UpdatedByAdminID null.Int     `gorm:"null" form:"updated_by_admin_id" json:"updated_by_admin_id"` // 更新的管理員ID
	UpdatedBy        string       `gorm:"-" form:"updated_by" json:"updated_by"`
	CreatedAt        time.Time    `gorm:"<-:create" form:"created_at" json:"created_at"`
	UpdatedAt        time.Time    `form:"updated_at" json:"updated_at"`
}

func (MemberVerifyHistory) TableName() string {
	return "member_verify_histories"
}

// Init member register, set memberID before call this function
func (memReg *MemReg) Init(conn *gorm.DB, isRegister bool) (string, error) {
	if memReg.MemberID == 0 {
		return "查無會員資料", nil
	}

	mem := &Member{}
	if err := conn.Select("id").First(&mem, memReg.MemberID).Error; err != nil {
		return "查無會員資料", err
	}

	// check member register
	if err := conn.Select("id").Where("member_id = ?", mem.ID).First(&memReg).Error; err == nil {
		return "已有註冊資料", nil
	}

	if isRegister {
		memReg.VerifyStep = VerifyStepOfficial
		memReg.Info.Status = VerifyWait
		memReg.Info.SubmittedAt = null.TimeFrom(time.Now())
	} else {
		memReg.VerifyStep = VerifyStepInfo
		memReg.Info.Status = VerifyIng
	}

	memReg.MemberID = mem.ID
	memReg.Info.MemberID = mem.ID
	memReg.Official.MemberID = mem.ID
	memReg.Official.Status = VerifyIng
	memReg.Real.MemberID = mem.ID
	memReg.Quiz.MemberID = mem.ID

	if err := conn.Create(&memReg).Error; err != nil {
		return "無法建立註冊資料", err
	}

	return "", nil
}

func UpdateVerifyStep(conn *gorm.DB, memberID uint, step int, nextStep int) error {
	if step > 0 {
		conn.Where("verify_step = ?", step)
	}
	return conn.Model(MemReg{}).Where("member_id = ?", memberID).Update("verify_step", nextStep).Error
}
